<template>
  <div class="huida-container theme-background">
    <!-- 顶部导航栏 -->
    <Maintopbar
      :show-home-btn="true"
      :show-back-to-index-btn="true"
      :show-feature-intro="true"
      @home="handleHome"
      @back-to-index="handleBackToIndex"
    />

    <!-- 提醒内容容器 - 暂时为空 -->
    <div class="reminder-content-container">
      <!-- 暂时为空，后续可添加提醒内容 -->
    </div>

    <!-- 中间聊天内容容器 -->
    <div class="huida-content-container">
      <div class="chat-content">
        <!-- 聊天内容区域 -->
        <div ref="scrollWrapper" class="chat-messages">
          <!-- 如果没有聊天消息，显示欢迎信息 -->
          <div v-if="chatMessages.length === 0" class="welcome-message">
            <div class="assistant-avatar">
              <img src="@/assets/assistant/董会答.png" alt="董会答" />
            </div>
            <div class="welcome-text">
              <div class="welcome-title">董会答</div>
              <div class="welcome-subtitle">随时问，随时答</div>
              <div class="welcome-description">可以问我任何问题</div>
            </div>
          </div>

          <!-- 聊天消息列表 -->
          <template v-for="(item, index) in chatMessages" :key="item.key">
            <ChatItem
              :message-data="item"
              :is-regenerate="index === chatMessages.length - 1"
              @regenerate="handleRegenerate"
            />
          </template>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="footer">
      <!-- 输入框 -->
      <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
        <inputBar
          ref="inputBarRef"
          @voice-send="handleInputSend"
          @send="handleInputSend"
          @stop="handleStop"
          @recording-status="handleRecordingStatus"
        />
      </form>

      <!-- 老董假装说话样式 - 直接放在输入框下方 -->
      <div class="laodong-fake-speaking">
        <div class="fake-speaking-container">
          <div class="laodong-avatar">
            <img src="@/assets/assistant/董会答.png" alt="董会答头像" />
          </div>
          <div class="fake-speaking-content">
            <div class="fake-speaking-text">董会答会根据您的问题给出专业建议</div>
            <div class="fake-speaking-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';
import { showFailToast } from 'vant';
import Maintopbar from '@/components/Maintopbar.vue';
import inputBar from '@/components/Chat/inputBar.vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';
import { Typewriter } from '@/utils/typeWriter';
import { streamChat, createConversation } from '@/apis/chat';
import { AnswerStatusEnum } from '@/constants/chat';
import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';
import { generateRandomString } from '@/utils';
import { getUserInfo } from '@/apis/common';

const router = useRouter();
const chatStore = useChatStore();
const userStore = useUserStore();

// 输入框引用
const inputBarRef = ref<InstanceType<typeof inputBar> | null>(null);

// 录音状态
const isRecording = ref(false);

// 聊天相关状态
const chatMessages = ref<IChatStreamContent[]>([]);
const conversationId = ref('');
const currentUserId = ref('');
const isStoppedByUser = ref(false);
const streamController = ref<AbortController | null>(null);
const isTypewriterStarted = ref(false);
const canSendMessage = ref(true);

// 初始化状态标志
const isInitialized = ref(false);

// 滚动容器引用
const scrollWrapper = ref<HTMLElement | null>(null);

// 创建打字机实例
const typewriter = new Typewriter(
  async (str: string) => {
    if (str) {
      console.log('🖨️ [huida.vue] typewriter更新消息内容:', {
        displayLength: str.length,
        preview: str.substring(0, 50) + (str.length > 50 ? '...' : ''),
        messageIndex: chatMessages.value.length - 1,
        isStoppedByUser: isStoppedByUser.value,
      });

      // 添加安全检查，确保消息索引有效
      if (chatMessages.value.length > 0) {
        const lastMessage = chatMessages.value[chatMessages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          lastMessage.content = str;
          await nextTick(() => {
            scrollToEnd();
          });
        } else {
          console.warn('⚠️ [huida.vue] typewriter回调时最后一条消息不是助手消息');
        }
      } else {
        console.warn('⚠️ [huida.vue] typewriter回调时消息列表为空');
      }
    }
  },
  () => {
    // 打字机自动完成回调
    console.log('✅ [huida.vue] typewriter自动完成回调触发');
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
    isTypewriterStarted.value = false;
    // 打字机完成时，允许发送新消息
    canSendMessage.value = true;
    streamController.value = null;
  },
);

// 滚动到底部
const scrollToEnd = () => {
  if (scrollWrapper.value) {
    scrollWrapper.value.scrollTop = scrollWrapper.value.scrollHeight;
  }
};

// 处理表单提交
const handleFormSubmit = () => {
  // 阻止默认表单提交行为
};

// 处理输入发送
const handleInputSend = async (message: string) => {
  console.log('🔄 [huida.vue] 发送消息:', message);

  if (!message.trim()) return;

  // 检查是否可以发送消息（打字机工作期间禁止发送）
  if (!canSendMessage.value) {
    console.log('🚫 [huida.vue] 打字机正在工作，禁止发送新消息');
    showFailToast('请等待当前回复完成后再发送消息');
    return;
  }

  // 检查用户信息是否已加载
  if (!currentUserId.value || currentUserId.value === 'unknown_user') {
    showFailToast('用户信息未加载，请刷新页面重试');
    return;
  }

  await sendChatMessage(message);
};

// 发送聊天消息
const sendChatMessage = async (messageContent: string) => {
  if (!messageContent.trim() || !currentUserId.value) {
    return;
  }

  console.log('🚀 [huida.vue] 开始发送聊天消息:', messageContent);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [huida.vue] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;
  canSendMessage.value = false;

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  // 添加用户消息
  const userMessage: IChatStreamContent = {
    role: 'user',
    content: messageContent,
    key: Date.now(),
    isFinish: true,
    reasoningData: {} as IReasoningData,
    isToolCallLoading: false,
  };
  chatMessages.value.push(userMessage);

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
    reasoningData: {} as IReasoningData,
    isToolCallLoading: false,
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollToEnd();
  });

  // 创建新的AbortController
  streamController.value = new AbortController();

  // 确保会话已创建（在发送第一条消息时创建）
  const currentConversationId = await createConversationIfNeeded();

  // 准备请求数据
  const requestData = {
    user_id: currentUserId.value,
    conversation_id: currentConversationId,
    content: messageContent,
    stream: true,
  };

  try {
    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (content: string, isFinal: boolean) => {
          if (isStoppedByUser.value) {
            console.log('🚫 [huida.vue] 用户已停止，忽略消息片段:', {
              contentLength: content.length,
              preview: content.substring(0, 30) + (content.length > 30 ? '...' : ''),
            });
            return;
          }

          // 清空预响应内容（正式回答开始时）
          const lastMessage = chatMessages.value[chatMessages.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant' && lastMessage.preResponseContent) {
            console.log('🔄 [huida.vue] 清空预响应内容，开始正式回答');
            lastMessage.preResponseContent = '';
            lastMessage.key = Date.now(); // 触发响应式更新
          }

          // 统一处理：无论是否为最终消息，都先添加到打字机队列
          typewriter.add(content);

          // 启动打字机（如果还未启动）
          if (!isTypewriterStarted.value) {
            console.log('🚀 [huida.vue] 启动typewriter，开始显示内容');
            isTypewriterStarted.value = true;
            typewriter.start();
          }

          if (isFinal) {
            console.log('✅ [huida.vue] 收到最终消息，标记完成');
            typewriter.markFinished();
            const lastMessage = chatMessages.value[chatMessages.value.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
              lastMessage.isFinish = true;
            }
          }
        },
        onPreResponse: (content: string, stage: string) => {
          console.log('🔍 [huida.vue] 收到预响应内容:', content, stage);
          // 显示预响应内容
          const lastMessage = chatMessages.value[chatMessages.value.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.preResponseContent = content;
            lastMessage.key = Date.now(); // 触发响应式更新
          }
        },
        onToolCall: (toolCall) => {
          console.log('🔧 [huida.vue] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations) => {
          console.log('💡 [huida.vue] 推荐问题:', recommendations);
        },
        onEnd: () => {
          console.log('🏁 [huida.vue] 流式聊天结束');
        },
        onError: (error) => {
          console.error('❌ [huida.vue] 流式聊天错误:', error);
          chatStore.setAnswerStatus(AnswerStatusEnum.FAIL);
          canSendMessage.value = true;
          streamController.value = null;
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [huida.vue] 发送消息失败:', error);
    chatStore.setAnswerStatus(AnswerStatusEnum.FAIL);
    canSendMessage.value = true;
    streamController.value = null;
  }
};

// 处理停止
const handleStop = () => {
  console.log('🔄 [huida.vue] 停止操作');
  if (streamController.value) {
    isStoppedByUser.value = true;
    streamController.value.abort();
    streamController.value = null;
    typewriter.stop();
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
    canSendMessage.value = true;
  }
};

// 处理录音状态变化
const handleRecordingStatus = (status: boolean) => {
  isRecording.value = status;
  console.log('🔄 [huida.vue] 录音状态变化:', status);
};

// 处理重新生成
const handleRegenerate = () => {
  console.log('🔄 [huida.vue] 重新生成回答');
  // 获取最后一条用户消息
  const userMessages = chatMessages.value.filter(msg => msg.role === 'user');
  if (userMessages.length > 0) {
    const lastUserMessage = userMessages[userMessages.length - 1];
    // 移除最后一条助手消息
    const lastIndex = chatMessages.value.findLastIndex(msg => msg.role === 'assistant');
    if (lastIndex !== -1) {
      chatMessages.value.splice(lastIndex, 1);
    }
    // 重新发送最后一条用户消息
    sendChatMessage(lastUserMessage.content);
  }
};

// 清理会话（开始新对话）
const clearChatSession = async () => {
  console.log('🔄 [huida.vue] 清理会话，开始新对话');

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();

  // 重置状态
  isStoppedByUser.value = false;
  isTypewriterStarted.value = false;

  // 重新初始化新会话（会清空会话ID，等待下次发送消息时重新创建）
  await initNewChat();
};

// 处理Home按钮点击 - 返回主页
const handleHome = async () => {
  console.log('🔄 [huida.vue] Home按钮点击，返回主页');
  await router.push({ name: 'chat' });
};

// 处理返回首页按钮点击
const handleBackToIndex = async () => {
  console.log('🔄 [huida.vue] 返回首页按钮点击');
  await router.push({ name: 'chat' });
};

// 初始化新会话方法（不创建会话，只重置状态）
const initNewChat = async () => {
  chatMessages.value = []; // 清空消息列表
  conversationId.value = ''; // 清空会话ID，等待第一条消息时创建

  // 重置打字机启动标志
  isTypewriterStarted.value = false;
  // 重置发送消息标志，允许发送新消息
  canSendMessage.value = true;
  // 标记已初始化
  isInitialized.value = true;
  console.log('✅ [huida.vue] 新会话已初始化（等待第一条消息时创建会话）');
};

// 获取用户信息
const loadUserInfo = async () => {
  try {
    console.log('🔄 [huida.vue] 开始获取用户信息...');
    const userInfo = await getUserInfo();
    console.log('📡 [huida.vue] 用户信息:', userInfo);

    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      userStore.userInfo = userInfo;
      console.log('✅ [huida.vue] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [huida.vue] 用户信息格式异常');
      // 设置默认值，避免连接失败
      currentUserId.value = 'unknown_user';
    }
  } catch (error) {
    console.error('❌ [huida.vue] 获取用户信息失败:', error);
    currentUserId.value = 'unknown_user';
  }
};

// 创建会话（在发送第一条消息时调用）
const createConversationIfNeeded = async () => {
  // 如果已经有会话ID，直接返回
  if (conversationId.value) {
    return conversationId.value;
  }

  try {
    if (currentUserId.value && currentUserId.value !== 'unknown_user') {
      const response = await createConversation({
        user_id: currentUserId.value,
      });

      if (response.success && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [huida.vue] 创建新会话成功，会话ID:', conversationId.value);
        return conversationId.value;
      } else {
        throw new Error('创建会话失败');
      }
    } else {
      throw new Error('用户信息无效');
    }
  } catch (error) {
    console.error('❌ [huida.vue] 创建会话失败:', error);
    // 如果接口调用失败，回退到生成随机ID
    conversationId.value = generateRandomString(16);
    console.log('🔄 [huida.vue] 回退到生成随机会话ID:', conversationId.value);
    return conversationId.value;
  }
};

// 页面挂载前的初始化
onBeforeMount(async () => {
  // 先加载用户信息
  await loadUserInfo();
});

// 页面挂载时的初始化
onMounted(async () => {
  console.log('🔄 [huida.vue] 页面挂载完成');

  // 设置功能标记
  sessionStorage.setItem('chatFeature', 'question');

  // 再次加载用户信息（确保用户信息已加载）
  await loadUserInfo();

  // 如果还没有初始化，则进行初始化
  if (!isInitialized.value) {
    console.log('🔄 [huida.vue] onMounted中进行初始化');
    await initNewChat();
  }
});

// 组件卸载时的清理工作
onUnmounted(() => {
  console.log('🔄 [huida.vue] 组件已卸载');

  // 如果有正在进行的请求，取消它
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.stop();

  // 重置初始化标志
  isInitialized.value = false;
});
</script>

<style lang="scss" scoped>
.huida-container {
  // 背景现在由主题系统控制
  height: 100vh; // 使用视口高度
  overflow: hidden;
  display: flex; // 添加flex布局
  flex-direction: column; // 纵向排列
  position: relative; // 为星空粒子和弹幕组件提供定位基准

  // 虚拟键盘兼容性处理
  @supports (height: 100dvh) {
    height: 100dvh; // 使用动态视口高度，更好地处理虚拟键盘
  }

  // 额外的虚拟键盘处理
  @supports (height: 100svh) {
    height: 100svh; // 使用小视口高度，在某些浏览器中更稳定
  }
}

.reminder-content-container {
  // 暂时为空，预留空间
  min-height: 0;
  flex-shrink: 0;
}

.huida-content-container {
  flex: 1;
  padding: 0px 0px 12px 0px;
  box-sizing: border-box;
  overflow: hidden;

  .chat-content {
    width: 100%;
    height: 100%;

    .chat-messages {
      width: 100%;
      height: 100%;
      padding: 32px 32px 80px 32px; // 增加底部padding，为预设问题和输入框留出充足空间，避免遮挡聊天内容
      overflow-y: auto;
      box-sizing: border-box;

      .welcome-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;

        .assistant-avatar {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          margin-bottom: 20px;
          border: 3px solid var(--border-accent);
          box-shadow: var(--shadow-medium);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .welcome-text {
          .welcome-title {
            font-size: calc(var(--font-size-3xl) + 4px);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
          }

          .welcome-subtitle {
            font-size: calc(var(--font-size-xl) + 4px);
            color: var(--text-secondary);
            margin-bottom: 12px;
          }

          .welcome-description {
            font-size: calc(var(--font-size-lg) + 4px);
            color: var(--text-tertiary);
          }
        }
      }
    }
  }
}

.footer {
  flex-shrink: 0;
  position: relative;
  z-index: 100;

  .input-wrapper {
    width: 100%;
    padding: 0px;
    background: transparent;
  }
}

// 老董假装说话样式 - 作为输入框的下半部分
.laodong-fake-speaking {
  padding: 0px 20px;
  display: flex;
  justify-content: flex-start;
  // 透明背景，与inputBar保持一致
  background: transparent;
  border: 2px solid var(--border-accent); // 添加左右边框
  border-top: none; // 去掉上边框
  border-radius: 0 0 20px 20px; // 只有下方圆角，与输入框形成整体
  margin-top: -2px; // 与输入框无缝连接
  // 移除模糊效果

  .fake-speaking-container {
    display: flex;
    align-items: center; // 改为居中对齐，一行显示
    gap: 8px; // 减少间距，从12px改为8px
    width: 100%; // 占满宽度

    .laodong-avatar {
      width: 45px; // 缩小头像，从80px改为54px（约缩小1/3）
      height: 45px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: #000000; /* 改为黑色 */
        font-size: 28px; // 继续增大字体
        font-weight: 400;
        line-height: 1.2;
        white-space: nowrap; // 不换行
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #000000; /* 改为黑色 */
          animation: fakeSpeakingPulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

// 假装说话动画
@keyframes fakeSpeakingPulse {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}
</style>
